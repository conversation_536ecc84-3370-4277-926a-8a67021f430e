import { MaterialIcons } from '@expo/vector-icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { Skeleton } from 'moti/skeleton';
import React from 'react';
import { Image, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useCachedFixtures } from '../hooks/useCachedData';
import { useManager } from '../context/ManagerContext';
import { useTheme } from '../theme/ThemeContext';
import { formatPlayerValue } from '../utils/PlayerUtils';
import { Text } from './Text';

dayjs.extend(relativeTime);

interface StyledProps {
  theme: DefaultTheme;
}

const WidgetContainer = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 20px;
  padding: 24px;
  margin: 20px;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 4px;
      shadow-opacity: 0.25;
      shadow-radius: 6px;
    `,
    android: `
      elevation: 6;
    `,
  })}
`;

const TeamHeader = styled.View`
  align-items: center;
  margin-bottom: 24px;
`;

const TeamName = styled(Text)`
  font-size: 28px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  text-align: center;
  margin-bottom: 16px;
`;

const KitContainer = styled.View`
  width: 80px;
  height: 80px;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 40px;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  border: 2px solid ${(props: StyledProps) => props.theme.colors.border};
`;

const BalanceRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
`;

const BalanceIcon = styled.Image`
  width: 28px;
  height: 28px;
  margin-right: 8px;
`;

const BalanceText = styled(Text)`
  font-size: 20px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const MatchSection = styled.View`
  margin-bottom: 16px;
`;

const MatchHeader = styled(Text)`
  font-size: 16px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const MatchCard = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 16px;
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
`;

const MatchRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const TeamContainer = styled.View`
  flex: 1;
`;

const TeamText = styled(Text)<{ isCurrentTeam?: boolean }>`
  font-size: 14px;
  color: ${(props: StyledProps & { isCurrentTeam?: boolean }) => 
    props.isCurrentTeam ? props.theme.colors.primary : props.theme.colors.text.primary};
  font-family: ${(props: { isCurrentTeam?: boolean }) => 
    props.isCurrentTeam ? 'NunitoBold' : 'Nunito'};
  text-align: ${(props: { isCurrentTeam?: boolean }) => 
    props.isCurrentTeam ? 'left' : 'right'};
`;

const ScoreContainer = styled.View`
  align-items: center;
  margin: 0 16px;
`;

const ScoreText = styled(Text)`
  font-size: 18px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const DateText = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
  margin-top: 8px;
`;

const NoMatchText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
  font-style: italic;
`;

export const TeamInfoWidget: React.FC = () => {
  const { team, loading } = useManager();
  const { manager } = useManager();
  const { isDark } = useTheme();
  
  const { data: fixtures, isLoading: fixturesLoading } = useCachedFixtures(
    manager?.gameworldId,
    team?.league.id,
    team?.teamId
  );

  const getNextFixture = () => {
    const now = dayjs();
    if (!fixtures || fixtures.length === 0) return null;
    return fixtures.find((fixture) => dayjs(fixture.date) > now);
  };

  const getLastFixture = () => {
    const now = dayjs();
    if (!fixtures || fixtures.length === 0) return null;
    return fixtures
      .filter((fixture) => dayjs(fixture.date) < now && fixture.score)
      .sort((a, b) => b.date - a.date)[0];
  };

  const nextFixture = getNextFixture();
  const lastFixture = getLastFixture();

  if (loading) {
    return (
      <WidgetContainer>
        <TeamHeader>
          <Skeleton width={200} height={32} radius={8} colorMode={isDark ? 'dark' : 'light'} />
          <KitContainer>
            <Skeleton
              width={80}
              height={80}
              radius={40}
              colorMode={isDark ? 'dark' : 'light'}
            />
          </KitContainer>
          <Skeleton width={150} height={24} radius={6} colorMode={isDark ? 'dark' : 'light'} />
        </TeamHeader>
      </WidgetContainer>
    );
  }

  return (
    <WidgetContainer>
      <TeamHeader>
        <TeamName>{team?.teamName || 'Your Team'}</TeamName>
        
        <KitContainer>
          <MaterialIcons name="sports-soccer" size={40} color="#666" />
        </KitContainer>

        <BalanceRow>
          <BalanceIcon source={require('../../assets/quid.svg')} />
          <BalanceText>{formatPlayerValue(team?.balance ?? 0)}</BalanceText>
        </BalanceRow>
      </TeamHeader>

      {nextFixture && (
        <MatchSection>
          <MatchHeader>Next Match</MatchHeader>
          <MatchCard>
            <MatchRow>
              <TeamContainer>
                <TeamText isCurrentTeam={nextFixture.homeTeamName === team?.teamName}>
                  {nextFixture.homeTeamName}
                </TeamText>
              </TeamContainer>
              <ScoreContainer>
                <ScoreText>vs</ScoreText>
              </ScoreContainer>
              <TeamContainer>
                <TeamText isCurrentTeam={nextFixture.awayTeamName === team?.teamName}>
                  {nextFixture.awayTeamName}
                </TeamText>
              </TeamContainer>
            </MatchRow>
            <DateText>{dayjs(nextFixture.date).format('ddd D MMM HH:mm')}</DateText>
          </MatchCard>
        </MatchSection>
      )}

      {lastFixture && (
        <MatchSection>
          <MatchHeader>Last Match</MatchHeader>
          <MatchCard>
            <MatchRow>
              <TeamContainer>
                <TeamText isCurrentTeam={lastFixture.homeTeamName === team?.teamName}>
                  {lastFixture.homeTeamName}
                </TeamText>
              </TeamContainer>
              <ScoreContainer>
                <ScoreText>
                  {lastFixture.score ? `${lastFixture.score[0]} - ${lastFixture.score[1]}` : '-'}
                </ScoreText>
              </ScoreContainer>
              <TeamContainer>
                <TeamText isCurrentTeam={lastFixture.awayTeamName === team?.teamName}>
                  {lastFixture.awayTeamName}
                </TeamText>
              </TeamContainer>
            </MatchRow>
            <DateText>{dayjs(lastFixture.date).format('ddd D MMM')}</DateText>
          </MatchCard>
        </MatchSection>
      )}

      {!nextFixture && !lastFixture && !fixturesLoading && (
        <MatchSection>
          <MatchHeader>Fixtures</MatchHeader>
          <MatchCard>
            <NoMatchText>No fixtures available</NoMatchText>
          </MatchCard>
        </MatchSection>
      )}
    </WidgetContainer>
  );
};
