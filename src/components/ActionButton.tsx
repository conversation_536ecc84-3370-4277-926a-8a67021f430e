import styled from 'styled-components/native';
import { Text } from './Text';

export const ActionButton = styled.TouchableOpacity<{
  variant?: 'primary' | 'secondary' | 'danger';
}>`
  flex: 1;
  padding: 20px;
  margin: 0 0 10px 0;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.variant) {
      case 'primary':
        return props.theme.colors.button.primary;
      case 'danger':
        return props.theme.colors.button.error;
      case 'secondary':
        return props.theme.colors.button.warning;
      default:
        return props.theme.colors.button.primary;
    }
  }};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
`;
export const ActionButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 14px;
  text-align: center;
`;
