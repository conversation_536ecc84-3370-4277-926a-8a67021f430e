import React from 'react';
import { Modal, TextInput } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { ActiveTransfer } from '../../hooks/useMyBidsPlayers';
import { TransferListPlayer } from '../../models/player';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { useAuctionTimer } from './hooks/useAuctionTimer';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferModalProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isAuctionPlayer: boolean;
  formattedValue: string;
  onClose: () => void;
  onNewTransfer?: (transfer: ActiveTransfer) => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: left;
  flex: 1;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-family: 'Nunito';
  font-size: 16px;
  margin-bottom: 8px;
`;

const Input = styled(TextInput)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  font-family: 'Nunito';
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  flex: 1;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const TransferModal: React.FC<TransferModalProps> = ({
  player,
  playerStatus,
  isAuctionPlayer,
  formattedValue,
  onClose,
  onNewTransfer,
}) => {
  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';
  const { playerActionsState, playerActions } = usePlayerUtils();

  const { timeRemaining } = useAuctionTimer(player);

  /*useEffect(() => {
    if (!isAuctionPlayer && playerActionsState.isOfferModalVisible) {
      playerActions.setOfferAmount(Math.ceil(player.value));
    }
  }, [
    isAuctionPlayer,
    player.value,
    playerActionsState.isOfferModalVisible,
    playerActions,
    formattedValue,
  ]);*/

  const handleSubmitOffer = async () => {
    await playerActions.handleSubmitOffer(
      player,
      playerStatus.isAuctionPlayer,
      currentBid,
      onNewTransfer
    );
  };

  const roundDownToPowerOf10 = (value: number): number => {
    if (value <= 0) return 0;
    const magnitude = Math.floor(Math.log10(value));
    return Math.floor(value / Math.pow(10, magnitude)) * Math.pow(10, magnitude);
  };
  const increment = roundDownToPowerOf10(playerActionsState.maxBid || currentBid) * 0.1;

  return (
    <Modal visible={playerActionsState.isOfferModalVisible} transparent onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>{player.teamId === '' ? 'Submit Bid' : 'Submit Transfer Offer'}</ModalTitle>

          <InputContainer>
            <Label>
              Player: {player.firstName} {player.surname}
            </Label>

            {isAuctionPlayer ? (
              <>
                <Label>Player Value: {formattedValue}</Label>
                <Label>
                  Current Bid:{' '}
                  {playerActionsState.maxBid
                    ? formatPlayerValue(playerActionsState.maxBid)
                    : formattedCurrentBid}
                </Label>
                {playerActionsState.maxBid && (
                  <Label
                    style={{
                      color: playerActionsState.isHighestBidder ? '#2E7D32' : '#e3172a',
                      fontFamily: 'NunitoBold',
                    }}
                  >
                    {playerActionsState.isHighestBidder
                      ? '✓ You are the highest bidder!'
                      : '✗ You are not the highest bidder'}
                  </Label>
                )}
                <Label>Time Remaining: {timeRemaining}</Label>
                <Label>
                  Minimum Bid:{' '}
                  {formatPlayerValue(
                    Math.ceil((playerActionsState.maxBid || currentBid) + increment)
                  )}
                </Label>
                <Label>Your Bid:</Label>
              </>
            ) : (
              <>
                <Label>Current Value: {formattedValue}</Label>
                <Label>Offer Amount:</Label>
              </>
            )}

            <Input
              value={playerActionsState.offerAmount.toString()}
              onChangeText={(val) => playerActions.setOfferAmount(Math.ceil(Number(val)))}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
          </InputContainer>

          <ButtonContainer>
            <TransferButton onPress={onClose} style={{ backgroundColor: '#888' }}>
              <ButtonText>Cancel</ButtonText>
            </TransferButton>
            <TransferButton onPress={handleSubmitOffer} disabled={playerActionsState.isSubmitting}>
              <ButtonText>
                {playerActionsState.isSubmitting
                  ? 'Submitting...'
                  : isAuctionPlayer
                    ? 'Submit Bid'
                    : 'Submit Offer'}
              </ButtonText>
            </TransferButton>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
