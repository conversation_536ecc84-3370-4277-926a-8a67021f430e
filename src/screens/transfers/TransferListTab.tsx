import React, { useState } from 'react';
import { ActivityIndicator, FlatList, Platform } from 'react-native';
import styled from 'styled-components/native';
import PlayerDetailView from '../../components/PlayerDetailView';
import TransferListPlayerRow from '../../components/PlayerRow/TransferListPlayerRow';
import {
  ButtonText,
  Container,
  EmptyListContainer,
  ListContainer,
  ListHeaderText,
  LoadingContainer,
  LoadMoreButton,
} from '../../components/TransferSharedStyles';
import { useDataCache } from '../../context/DataCacheContext';
import { useManager } from '../../context/ManagerContext';
import { PlayerProvider } from '../../context/PlayerContext';
import {
  useCachedMyBidsPlayers,
  useCachedTeam,
  useCachedTransferListPlayers,
} from '../../hooks/useCachedData';
import { TransferListPlayer } from '../../models/player';
import { SharedPositionFilter } from './components/SharedPositionFilter';
import {
  TokenCountContainer,
  TokenCountText,
  TokenIcon,
  TokenTypeContainer,
} from './components/TokenCountContainer';
import { useTeamAverages } from './hooks/useTeamAverages';
import { useTransferListLogic } from './hooks/useTransferListLogic';
styled(EmptyListContainer)`
  padding: 20px;
`;
type ListItem = {
  type: 'header' | 'empty' | 'myBids' | 'transferList';
  data: TransferListPlayer | string;
};

const TransferListTab = () => {
  const { manager } = useManager();
  const { findPlayer } = useDataCache();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastEvaluatedKey, setLastEvaluatedKey] = useState<string | undefined>(undefined);

  // Data fetching hooks
  const { data: transferListData, isLoading: isLoadingTransferList } = useCachedTransferListPlayers(
    manager?.gameworldId,
    lastEvaluatedKey
  );

  const { data: myBidsData, isLoading: isLoadingMyBids } = useCachedMyBidsPlayers(
    manager?.gameworldId
  );

  const { team: teamData, isLoading: isLoadingTeam } = useCachedTeam(
    manager?.gameworldId,
    manager?.team?.teamId
  );

  // Custom hooks for business logic
  const transferListLogic = useTransferListLogic({
    transferListData,
    myBidsData,
    isLoadingMore,
    lastEvaluatedKey,
  });

  const teamAverages = useTeamAverages(teamData?.players);

  // Loading state management
  const isLoading = isLoadingTransferList || isLoadingMyBids || isLoadingTeam;

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (transferListLogic.reachedEnd) {
      return;
    }

    const paginationKey = transferListLogic.getPaginationKey(transferListData);

    if (paginationKey && paginationKey !== lastEvaluatedKey) {
      setIsLoadingMore(true);
      setLastEvaluatedKey(paginationKey);
    }
  };

  // Function to check if the user's team is the highest bidder
  const isUserHighestBidder = (player: TransferListPlayer): boolean => {
    if (!manager?.team || !player.bidHistory || player.bidHistory.length === 0) {
      return false;
    }

    // Sort bid history by maximum bid (highest first)
    const sortedBids = [...player.bidHistory].sort((a, b) => b.maximumBid - a.maximumBid);

    // Check if the user's team is the highest bidder
    return sortedBids[0]?.teamId === manager.team.teamId;
  };

  const renderPlayer = ({ item }: { item: TransferListPlayer; index: number }) => {
    // Get the current player data from cache to ensure we show updated information
    const currentPlayer = findPlayer(item.playerId) as TransferListPlayer | null;
    const playerToRender = currentPlayer || item; // Fallback to original item if not found in cache

    return (
      <TransferListPlayerRow
        player={playerToRender}
        onSelect={transferListLogic.handlePlayerSelect}
        isSelected={transferListLogic.selectedPlayerId === item.playerId}
        positionFilter={transferListLogic.positionFilter}
        teamAverages={teamAverages[transferListLogic.positionFilter]}
        isHighestBidder={isUserHighestBidder(playerToRender)}
      />
    );
  };

  // Reset loading more state when loading finishes
  React.useEffect(() => {
    if (isLoadingMore && !isLoadingTransferList) {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, isLoadingTransferList]);

  if (isLoading && !isLoadingMore) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  // Create a combined data structure with sections
  const combinedData: ListItem[] = [];

  // Add my bids section if there are any bids
  if (transferListLogic.myBidsPlayers.length > 0) {
    combinedData.push({
      data: "Players You've Bid On",
      type: 'header',
    });
    combinedData.push(
      ...transferListLogic.myBidsPlayers.map((player) => ({
        data: player,
        type: 'myBids' as const,
      }))
    );
  }

  // Add transfer list section
  combinedData.push({
    data: 'Players Available for Transfer',
    type: 'header',
  });
  combinedData.push(
    ...transferListLogic.transferListPlayers.map((player) => ({
      data: player,
      type: 'transferList' as const,
    }))
  );

  return (
    <Container>
      <TokenCountContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../assets/quid.svg')} />
          <TokenCountText>{manager?.team?.balance?.toLocaleString() ?? '0'}</TokenCountText>
        </TokenTypeContainer>
      </TokenCountContainer>

      <SharedPositionFilter
        positionFilter={transferListLogic.positionFilter}
        onPositionSelect={transferListLogic.handlePositionSelect}
      />

      {/* Combined List with Sections */}
      <ListContainer style={Platform.OS === 'web' ? { position: 'relative', zIndex: 1 } : {}}>
        <FlatList<ListItem>
          data={combinedData}
          renderItem={({ item }) => {
            if (item.type === 'header') {
              return (
                <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>
                  {item.data as string}
                </ListHeaderText>
              );
            }

            return renderPlayer({ item: item.data as TransferListPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if (item.type === 'header') return `${item.type}-${index}`;
            return (item.data as TransferListPlayer).playerId;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            transferListLogic.getPaginationKey(transferListData) ? (
              <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
                {isLoadingMore ? (
                  <>
                    <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                    <ButtonText>Loading...</ButtonText>
                  </>
                ) : (
                  <ButtonText>Load More</ButtonText>
                )}
              </LoadMoreButton>
            ) : null
          }
        />
      </ListContainer>

      {/* Player detail view */}
      {transferListLogic.selectedPlayer && (
        <PlayerProvider>
          <PlayerDetailView
            player={transferListLogic.selectedPlayer}
            onClose={transferListLogic.clearSelectedPlayer}
            onNewTransfer={() => {}}
            onNegotiate={() => {}}
          />
        </PlayerProvider>
      )}
    </Container>
  );
};

export default TransferListTab;
